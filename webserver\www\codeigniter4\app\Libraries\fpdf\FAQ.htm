<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>FAQ</title>
<link type="text/css" rel="stylesheet" href="fpdf.css">
<style type="text/css">
ul {list-style-type:none; margin:0; padding:0}
ul#answers li {margin-top:1.8em}
.question {font-weight:bold; color:#900000}
</style>
</head>
<body>
<h1>FAQ</h1>
<ul>
<li><b>1.</b> <a href='#q1'>What's exactly the license of FPDF? Are there any usage restrictions?</a></li>
<li><b>2.</b> <a href='#q2'>I get the following error when I try to generate a PDF: Some data has already been output, can't send PDF file</a></li>
<li><b>3.</b> <a href='#q3'>Accented letters are replaced by some strange characters like Ã©.</a></li>
<li><b>4.</b> <a href='#q4'>I try to display the Euro symbol but it doesn't work.</a></li>
<li><b>5.</b> <a href='#q5'>I try to display a variable in the Header method but nothing prints.</a></li>
<li><b>6.</b> <a href='#q6'>I have defined the Header and Footer methods in my PDF class but nothing shows.</a></li>
<li><b>7.</b> <a href='#q7'>I can't make line breaks work. I put \n in the string printed by MultiCell but it doesn't work.</a></li>
<li><b>8.</b> <a href='#q8'>I use jQuery to generate the PDF but it doesn't show.</a></li>
<li><b>9.</b> <a href='#q9'>I draw a frame with very precise dimensions, but when printed I notice some differences.</a></li>
<li><b>10.</b> <a href='#q10'>I'd like to use the whole surface of the page, but when printed I always have some margins. How can I get rid of them?</a></li>
<li><b>11.</b> <a href='#q11'>How can I put a background in my PDF?</a></li>
<li><b>12.</b> <a href='#q12'>How can I set a specific header or footer on the first page?</a></li>
<li><b>13.</b> <a href='#q13'>I'd like to use extensions provided by different scripts. How can I combine them?</a></li>
<li><b>14.</b> <a href='#q14'>How can I open the PDF in a new tab?</a></li>
<li><b>15.</b> <a href='#q15'>How can I send the PDF by email?</a></li>
<li><b>16.</b> <a href='#q16'>What's the limit of the file sizes I can generate with FPDF?</a></li>
<li><b>17.</b> <a href='#q17'>Can I modify a PDF with FPDF?</a></li>
<li><b>18.</b> <a href='#q18'>I'd like to make a search engine in PHP and index PDF files. Can I do it with FPDF?</a></li>
<li><b>19.</b> <a href='#q19'>Can I convert an HTML page to PDF with FPDF?</a></li>
<li><b>20.</b> <a href='#q20'>Can I concatenate PDF files with FPDF?</a></li>
</ul>

<ul id='answers'>
<li id='q1'>
<p><b>1.</b> <span class='question'>What's exactly the license of FPDF? Are there any usage restrictions?</span></p>
FPDF is released under a permissive license: there is no usage restriction. You may embed it
freely in your application (commercial or not), with or without modifications.
</li>

<li id='q2'>
<p><b>2.</b> <span class='question'>I get the following error when I try to generate a PDF: Some data has already been output, can't send PDF file</span></p>
You must send nothing to the browser except the PDF itself: no HTML, no space, no carriage return. A common
case is having extra blank at the end of an included script file.<br>
<br>
The message may be followed by this indication:<br>
<br>
(output started at script.php:X)<br>
<br>
which gives you exactly the script and line number responsible for the output. If you don't see it,
try adding this line at the very beginning of your script:
<div class="doc-source">
<pre><code>ob_end_clean();</code></pre>
</div>
</li>

<li id='q3'>
<p><b>3.</b> <span class='question'>Accented letters are replaced by some strange characters like Ã©.</span></p>
Don't use UTF-8 with the standard fonts; they expect text encoded in windows-1252.
You can perform a conversion with iconv:
<div class="doc-source">
<pre><code>$str = iconv('UTF-8', 'windows-1252', $str);</code></pre>
</div>
Or with mbstring:
<div class="doc-source">
<pre><code>$str = mb_convert_encoding($str, 'windows-1252', 'UTF-8');</code></pre>
</div>
In case you need characters outside windows-1252, take a look at tutorial #7 or
<a href="http://www.fpdf.org/?go=script&amp;id=92" target="_blank">tFPDF</a>.
</li>

<li id='q4'>
<p><b>4.</b> <span class='question'>I try to display the Euro symbol but it doesn't work.</span></p>
The standard fonts have the Euro character at position 128. You can define a constant like this
for convenience:
<div class="doc-source">
<pre><code>define('EURO', chr(128));</code></pre>
</div>
</li>

<li id='q5'>
<p><b>5.</b> <span class='question'>I try to display a variable in the Header method but nothing prints.</span></p>
You have to use the <code>global</code> keyword to access global variables, for example:
<div class="doc-source">
<pre><code>function Header()
{
    global $title;

    $this-&gt;SetFont('Arial', 'B', 15);
    $this-&gt;Cell(0, 10, $title, 1, 1, 'C');
}

$title = 'My title';</code></pre>
</div>
Alternatively, you can use an object property:
<div class="doc-source">
<pre><code>function Header()
{
    $this-&gt;SetFont('Arial', 'B', 15);
    $this-&gt;Cell(0, 10, $this-&gt;title, 1, 1, 'C');
}

$pdf-&gt;title = 'My title';</code></pre>
</div>
</li>

<li id='q6'>
<p><b>6.</b> <span class='question'>I have defined the Header and Footer methods in my PDF class but nothing shows.</span></p>
You have to create an object from the PDF class, not FPDF:
<div class="doc-source">
<pre><code>$pdf = new PDF();</code></pre>
</div>
</li>

<li id='q7'>
<p><b>7.</b> <span class='question'>I can't make line breaks work. I put \n in the string printed by MultiCell but it doesn't work.</span></p>
You have to enclose your string with double quotes, not single ones.
</li>

<li id='q8'>
<p><b>8.</b> <span class='question'>I use jQuery to generate the PDF but it doesn't show.</span></p>
Don't use an AJAX request to retrieve the PDF.
</li>

<li id='q9'>
<p><b>9.</b> <span class='question'>I draw a frame with very precise dimensions, but when printed I notice some differences.</span></p>
To respect dimensions, select "None" for the Page Scaling setting instead of "Shrink to Printable Area" in the print dialog box.
</li>

<li id='q10'>
<p><b>10.</b> <span class='question'>I'd like to use the whole surface of the page, but when printed I always have some margins. How can I get rid of them?</span></p>
Printers have physical margins (different depending on the models); it is therefore impossible to remove
them and print on the whole surface of the paper.
</li>

<li id='q11'>
<p><b>11.</b> <span class='question'>How can I put a background in my PDF?</span></p>
For a picture, call Image() in the Header() method, before any other output. To set a background color, use Rect().
</li>

<li id='q12'>
<p><b>12.</b> <span class='question'>How can I set a specific header or footer on the first page?</span></p>
Just test the page number:
<div class="doc-source">
<pre><code>function Header()
{
    if($this-&gt;PageNo()==1)
    {
        //First page
        ...
    }
    else
    {
        //Other pages
        ...
    }
}</code></pre>
</div>
</li>

<li id='q13'>
<p><b>13.</b> <span class='question'>I'd like to use extensions provided by different scripts. How can I combine them?</span></p>
Use an inheritance chain. If you have two classes, say A in a.php:
<div class="doc-source">
<pre><code>require('fpdf.php');

class A extends FPDF
{
...
}</code></pre>
</div>
and B in b.php:
<div class="doc-source">
<pre><code>require('fpdf.php');

class B extends FPDF
{
...
}</code></pre>
</div>
then make B extend A:
<div class="doc-source">
<pre><code>require('a.php');

class B extends A
{
...
}</code></pre>
</div>
and make your own class extend B:
<div class="doc-source">
<pre><code>require('b.php');

class PDF extends B
{
...
}

$pdf = new PDF();</code></pre>
</div>
</li>

<li id='q14'>
<p><b>14.</b> <span class='question'>How can I open the PDF in a new tab?</span></p>
Just do the same as you would for an HTML page or anything else: add a target="_blank" to your link or form.
</li>

<li id='q15'>
<p><b>15.</b> <span class='question'>How can I send the PDF by email?</span></p>
As for any other file, but an easy way is to use <a href="https://github.com/PHPMailer/PHPMailer" target="_blank">PHPMailer</a> and
its in-memory attachment:
<div class="doc-source">
<pre><code>$mail = new PHPMailer();
...
$doc = $pdf-&gt;Output('S');
$mail-&gt;AddStringAttachment($doc, 'doc.pdf', 'base64', 'application/pdf');
$mail-&gt;Send();</code></pre>
</div>
</li>

<li id='q16'>
<p><b>16.</b> <span class='question'>What's the limit of the file sizes I can generate with FPDF?</span></p>
There is no particular limit. There are some constraints, however:
<br>
<br>
- There is usually a maximum memory size allocated to PHP scripts. For very big documents,
especially with images, the limit may be reached (the file being built in memory). The
parameter is configured in the php.ini file.
<br>
<br>
- The maximum execution time allocated to scripts defaults to 30 seconds. This limit can of course
be easily reached. It is configured in php.ini and may be altered dynamically with set_time_limit().
<br>
<br>
You can work around the memory limit with <a href="http://www.fpdf.org/?go=script&amp;id=76" target="_blank">this script</a>.
</li>

<li id='q17'>
<p><b>17.</b> <span class='question'>Can I modify a PDF with FPDF?</span></p>
It's possible to import pages from an existing PDF document thanks to the
<a href="https://www.setasign.com/products/fpdi/about/" target="_blank">FPDI</a> extension.
Then you can add some content to them.
</li>

<li id='q18'>
<p><b>18.</b> <span class='question'>I'd like to make a search engine in PHP and index PDF files. Can I do it with FPDF?</span></p>
No. But a GPL C utility does exist, pdftotext, which is able to extract the textual content from a PDF.
It's provided with the <a href="https://www.xpdfreader.com" target="_blank">Xpdf</a> package.
</li>

<li id='q19'>
<p><b>19.</b> <span class='question'>Can I convert an HTML page to PDF with FPDF?</span></p>
Not real-world pages. But a GPL C utility does exist, <a href="https://www.msweet.org/htmldoc/" target="_blank">HTMLDOC</a>,
which allows to do it and gives good results.
</li>

<li id='q20'>
<p><b>20.</b> <span class='question'>Can I concatenate PDF files with FPDF?</span></p>
Not directly, but it's possible to use <a href="https://www.setasign.com/products/fpdi/demos/concatenate-fake/" target="_blank">FPDI</a>
to perform that task. Some free command-line tools also exist:
<a href="https://www.pdflabs.com/tools/pdftk-the-pdf-toolkit/" target="_blank">pdftk</a> and
<a href="http://thierry.schmit.free.fr/spip/spip.php?article15" target="_blank">mbtPdfAsm</a>.
</li>
</ul>
</body>
</html>
