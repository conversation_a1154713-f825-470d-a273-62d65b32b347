DEBUG - 2025-06-11 19:59:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 19:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 19:59:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 19:59:41 --> [DEPRECATED] Creation of dynamic property App\Controllers\Cadastro::$clientes is deprecated in APPPATH/Controllers/Cadastro.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(903): App\Controllers\Cadastro->__construct()
 2 SYSTEMPATH/CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-11 19:59:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 19:59:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 19:59:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:09 --> Dados recebidos no cadastro: {"nome":"Nikolas","sobrenome":"de Hor","email":"<EMAIL>","cpf":"030.899.461-22","telefone":"(62) 98607-7431","senha":"Nikolas21.","confirmar_senha":"Nikolas21.","data_nasc":"2000-11-21","observacoes":""}
DEBUG - 2025-06-11 20:00:10 --> Validação passou, tentando salvar usuário...
DEBUG - 2025-06-11 20:00:10 --> Dados do usuário para salvar: {"usuarios_nome":"Nikolas","usuarios_sobrenome":"de Hor","usuarios_email":"<EMAIL>","usuarios_cpf":"030.899.461-22","usuarios_fone":"(62) 98607-7431","usuarios_senha":"9ebb8340fe036fc79b68aba3ad8ce5dc","usuarios_data_nasc":"2000-11-21","usuarios_data_cadastro":"2025-06-11 20:00:10","usuarios_nivel":0}
DEBUG - 2025-06-11 20:00:10 --> Resultado do save do usuário: true
DEBUG - 2025-06-11 20:00:10 --> ID do usuário inserido: 3
DEBUG - 2025-06-11 20:00:10 --> Dados do cliente para salvar: {"clientes_usuario_id":3,"clientes_observacoes":null,"created_at":"2025-06-11 20:00:10","updated_at":"2025-06-11 20:00:10"}
DEBUG - 2025-06-11 20:00:10 --> Resultado do save do cliente: true
DEBUG - 2025-06-11 20:00:10 --> Cadastro finalizado, redirecionando para login...
DEBUG - 2025-06-11 20:00:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:00:31 --> Security Event: LOGIN_SUCCESS
DEBUG - 2025-06-11 20:00:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:00:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:01:13 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:01:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:01:23 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:01:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:01:45 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:01:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:02:46 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:04:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:04:59 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:05:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:30 --> Dados recebidos no cadastro: {"nome":"Nikolas","sobrenome":"de Hor","email":"<EMAIL>","cpf":"030.899.461-22","telefone":"(62) 98607-7431","senha":"Nikolas21.","confirmar_senha":"Nikolas21.","data_nasc":"2000-11-21","observacoes":""}
DEBUG - 2025-06-11 20:05:30 --> Validação passou, tentando salvar usuário...
DEBUG - 2025-06-11 20:05:30 --> Dados do usuário para salvar: {"usuarios_nome":"Nikolas","usuarios_sobrenome":"de Hor","usuarios_email":"<EMAIL>","usuarios_cpf":"030.899.461-22","usuarios_fone":"(62) 98607-7431","usuarios_senha":"9ebb8340fe036fc79b68aba3ad8ce5dc","usuarios_data_nasc":"2000-11-21","usuarios_data_cadastro":"2025-06-11 20:05:30","usuarios_nivel":0}
DEBUG - 2025-06-11 20:05:30 --> Resultado do save do usuário: true
DEBUG - 2025-06-11 20:05:30 --> ID do usuário inserido: 4
DEBUG - 2025-06-11 20:05:30 --> Dados do cliente para salvar: {"clientes_usuario_id":4,"clientes_observacoes":null,"created_at":"2025-06-11 20:05:30","updated_at":"2025-06-11 20:05:30"}
DEBUG - 2025-06-11 20:05:30 --> Resultado do save do cliente: true
DEBUG - 2025-06-11 20:05:30 --> Cadastro finalizado, redirecionando para login...
DEBUG - 2025-06-11 20:05:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:05:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:06:20 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:06:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:06:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:07:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:08:03 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:08:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:10:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:11:04 --> Security Event: LOGIN_SUCCESS
DEBUG - 2025-06-11 20:11:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:11:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:12:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:13:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:14:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-11 20:17:30 --> mysqli_sql_exception: Unknown column 'p.pedidos_data_pedido' in 'field list' in /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php(327): mysqli->query('SELECT p.pedido...', 0)
#1 /var/www/html/codeigniter4/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedido...')
#2 /var/www/html/codeigniter4/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedido...')
#3 /var/www/html/codeigniter4/app/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedido...')
#4 /var/www/html/codeigniter4/system/View/View.php(224): include('/var/www/html/c...')
#5 /var/www/html/codeigniter4/system/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#6 /var/www/html/codeigniter4/system/Common.php(1173): CodeIgniter\View\View->render('admin/index', Array, true)
#7 /var/www/html/codeigniter4/app/Controllers/Admin.php(10): view('admin/index')
#8 /var/www/html/codeigniter4/system/CodeIgniter.php(933): App\Controllers\Admin->index()
#9 /var/www/html/codeigniter4/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
#10 /var/www/html/codeigniter4/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 /var/www/html/codeigniter4/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 /var/www/html/codeigniter4/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 /var/www/html/codeigniter4/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 {main}
CRITICAL - 2025-06-11 20:17:30 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'p.pedidos_data_pedido' in 'field list'
[Method: GET, Route: admin]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 2 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 3 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 5 APPPATH/Controllers/Admin.php(10): view('admin/index')
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
 7 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
 8 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:17:30 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'p.pedidos_data_pedido' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 3 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 4 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 5 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 7 APPPATH/Controllers/Admin.php(10): view('admin/index')
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:17:30 --> [Caused by] mysqli_sql_exception: Unknown column 'p.pedidos_data_pedido' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 4 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 5 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 6 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 7 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 8 APPPATH/Controllers/Admin.php(10): view('admin/index')
 9 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
10 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
11 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-11 20:17:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:17:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:18:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-11 20:19:08 --> mysqli_sql_exception: Unknown column 'created_at' in 'field list' in /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php(327): mysqli->query('INSERT INTO `ci...', 0)
#1 /var/www/html/codeigniter4/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ci...')
#2 /var/www/html/codeigniter4/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ci...')
#3 /var/www/html/codeigniter4/system/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ci...', Array, false)
#4 /var/www/html/codeigniter4/system/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 /var/www/html/codeigniter4/system/BaseModel.php(839): CodeIgniter\Model->doInsert(Array)
#6 /var/www/html/codeigniter4/system/Model.php(800): CodeIgniter\BaseModel->insert(Array, false)
#7 /var/www/html/codeigniter4/system/BaseModel.php(750): CodeIgniter\Model->insert(Array, false)
#8 /var/www/html/codeigniter4/app/Controllers/Cidades.php(55): CodeIgniter\BaseModel->save(Array)
#9 /var/www/html/codeigniter4/system/CodeIgniter.php(933): App\Controllers\Cidades->create()
#10 /var/www/html/codeigniter4/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Cidades))
#11 /var/www/html/codeigniter4/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#12 /var/www/html/codeigniter4/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#13 /var/www/html/codeigniter4/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#14 /var/www/html/codeigniter4/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#15 {main}
CRITICAL - 2025-06-11 20:19:08 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'field list'
[Method: POST, Route: cidades/create]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 SYSTEMPATH/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (:cidades_nome:, :cidades_uf:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH/BaseModel.php(839): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH/Model.php(800): CodeIgniter\BaseModel->insert([...], false)
 5 SYSTEMPATH/BaseModel.php(750): CodeIgniter\Model->insert([...], false)
 6 APPPATH/Controllers/Cidades.php(55): CodeIgniter\BaseModel->save([...])
 7 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Cidades->create()
 8 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Cidades))
 9 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:19:08 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'created_at' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (\'Rialma\', \'GO\', \'2025-06-11 20:19:08\', \'2025-06-11 20:19:08\')')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (\'Rialma\', \'GO\', \'2025-06-11 20:19:08\', \'2025-06-11 20:19:08\')')
 3 SYSTEMPATH/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (:cidades_nome:, :cidades_uf:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH/BaseModel.php(839): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH/Model.php(800): CodeIgniter\BaseModel->insert([...], false)
 7 SYSTEMPATH/BaseModel.php(750): CodeIgniter\Model->insert([...], false)
 8 APPPATH/Controllers/Cidades.php(55): CodeIgniter\BaseModel->save([...])
 9 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Cidades->create()
10 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Cidades))
11 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:19:08 --> [Caused by] mysqli_sql_exception: Unknown column 'created_at' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (\'Rialma\', \'GO\', \'2025-06-11 20:19:08\', \'2025-06-11 20:19:08\')', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (\'Rialma\', \'GO\', \'2025-06-11 20:19:08\', \'2025-06-11 20:19:08\')')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (\'Rialma\', \'GO\', \'2025-06-11 20:19:08\', \'2025-06-11 20:19:08\')')
 4 SYSTEMPATH/Database/BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `cidades` (`cidades_nome`, `cidades_uf`, `created_at`, `updated_at`) VALUES (:cidades_nome:, :cidades_uf:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH/Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH/BaseModel.php(839): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH/Model.php(800): CodeIgniter\BaseModel->insert([...], false)
 8 SYSTEMPATH/BaseModel.php(750): CodeIgniter\Model->insert([...], false)
 9 APPPATH/Controllers/Cidades.php(55): CodeIgniter\BaseModel->save([...])
10 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Cidades->create()
11 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Cidades))
12 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-11 20:19:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-11 20:19:20 --> mysqli_sql_exception: Unknown column 'p.pedidos_data_pedido' in 'field list' in /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php:327
Stack trace:
#0 /var/www/html/codeigniter4/system/Database/MySQLi/Connection.php(327): mysqli->query('SELECT p.pedido...', 0)
#1 /var/www/html/codeigniter4/system/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedido...')
#2 /var/www/html/codeigniter4/system/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedido...')
#3 /var/www/html/codeigniter4/app/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedido...')
#4 /var/www/html/codeigniter4/system/View/View.php(224): include('/var/www/html/c...')
#5 /var/www/html/codeigniter4/system/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#6 /var/www/html/codeigniter4/system/Common.php(1173): CodeIgniter\View\View->render('admin/index', Array, true)
#7 /var/www/html/codeigniter4/app/Controllers/Admin.php(10): view('admin/index')
#8 /var/www/html/codeigniter4/system/CodeIgniter.php(933): App\Controllers\Admin->index()
#9 /var/www/html/codeigniter4/system/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
#10 /var/www/html/codeigniter4/system/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 /var/www/html/codeigniter4/system/Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 /var/www/html/codeigniter4/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 /var/www/html/codeigniter4/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 {main}
CRITICAL - 2025-06-11 20:19:20 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'p.pedidos_data_pedido' in 'field list'
[Method: GET, Route: admin]
in SYSTEMPATH/Database/BaseConnection.php on line 692.
 1 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 2 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 3 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 5 APPPATH/Controllers/Admin.php(10): view('admin/index')
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
 7 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
 8 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:19:20 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'p.pedidos_data_pedido' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 332.
 1 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 2 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 3 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 4 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 5 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 7 APPPATH/Controllers/Admin.php(10): view('admin/index')
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
 9 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
10 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-11 20:19:20 --> [Caused by] mysqli_sql_exception: Unknown column 'p.pedidos_data_pedido' in 'field list'
in SYSTEMPATH/Database/MySQLi/Connection.php on line 327.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(327): mysqli->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5', 0)
 2 SYSTEMPATH/Database/BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 3 SYSTEMPATH/Database/BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 4 APPPATH/Views/admin/index.php(179): CodeIgniter\Database\BaseConnection->query('SELECT p.pedidos_id, u.usuarios_nome, p.pedidos_data_pedido, p.pedidos_status
                                        FROM pedidos p
                                        LEFT JOIN usuarios u ON p.pedidos_usuario_id = u.usuarios_id
                                        ORDER BY p.pedidos_data_pedido DESC LIMIT 5')
 5 SYSTEMPATH/View/View.php(224): include('/var/www/html/codeigniter4/app/Views/admin/index.php')
 6 SYSTEMPATH/View/View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 7 SYSTEMPATH/Common.php(1173): CodeIgniter\View\View->render('admin/index', [], true)
 8 APPPATH/Controllers/Admin.php(10): view('admin/index')
 9 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Admin->index()
10 SYSTEMPATH/CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin))
11 SYSTEMPATH/CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH/Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-11 20:20:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:20:46 --> Security Event: LOGIN_FAILED_INVALID_CREDENTIALS
DEBUG - 2025-06-11 20:20:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:20:56 --> Security Event: LOGIN_FAILED_INVALID_CREDENTIALS
DEBUG - 2025-06-11 20:20:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:21:18 --> Security Event: LOGIN_FAILED_INVALID_CREDENTIALS
DEBUG - 2025-06-11 20:21:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:21:42 --> Security Event: LOGIN_FAILED_USER_NOT_FOUND
DEBUG - 2025-06-11 20:21:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:21:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:15 --> Dados recebidos no cadastro: {"nome":"Nikolas","sobrenome":"Vale","email":"<EMAIL>","cpf":"030.899.461-22","telefone":"(62) 98607-7431","senha":"Nikolas21.","confirmar_senha":"Nikolas21.","data_nasc":"2000-11-21","observacoes":""}
DEBUG - 2025-06-11 20:22:15 --> Validação passou, tentando salvar usuário...
DEBUG - 2025-06-11 20:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:15 --> Dados do usuário para salvar: {"usuarios_nome":"Nikolas","usuarios_sobrenome":"Vale","usuarios_email":"<EMAIL>","usuarios_cpf":"030.899.461-22","usuarios_fone":"(62) 98607-7431","usuarios_senha":"9ebb8340fe036fc79b68aba3ad8ce5dc","usuarios_data_nasc":"2000-11-21","usuarios_data_cadastro":"2025-06-11 20:22:15","usuarios_nivel":0}
DEBUG - 2025-06-11 20:22:15 --> Resultado do save do usuário: true
DEBUG - 2025-06-11 20:22:15 --> ID do usuário inserido: 5
DEBUG - 2025-06-11 20:22:15 --> Dados do cliente para salvar: {"clientes_usuario_id":5,"clientes_observacoes":null,"created_at":"2025-06-11 20:22:15","updated_at":"2025-06-11 20:22:15"}
DEBUG - 2025-06-11 20:22:15 --> Resultado do save do cliente: true
DEBUG - 2025-06-11 20:22:15 --> Cadastro finalizado, redirecionando para login...
DEBUG - 2025-06-11 20:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:22:25 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:22:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:23:15 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:23:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:23:54 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:23:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:24:54 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:24:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-06-11 20:25:10 --> Security Event: LOGIN_RATE_LIMIT_EXCEEDED
DEBUG - 2025-06-11 20:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
