<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MultiCell</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>MultiCell</h1>
<code>MultiCell(<b>float</b> w, <b>float</b> h, <b>string</b> txt [, <b>mixed</b> border [, <b>string</b> align [, <b>boolean</b> fill]]])</code>
<h2>Description</h2>
This method allows printing text with line breaks. They can be automatic (as soon as the
text reaches the right border of the cell) or explicit (via the \n character). As many cells
as necessary are output, one below the other.
<br>
Text can be aligned, centered or justified. The cell block can be framed and the background
painted.
<h2>Parameters</h2>
<dl class="param">
<dt><code>w</code></dt>
<dd>
Width of cells. If <code>0</code>, they extend up to the right margin of the page.
</dd>
<dt><code>h</code></dt>
<dd>
Height of cells.
</dd>
<dt><code>txt</code></dt>
<dd>
String to print.
</dd>
<dt><code>border</code></dt>
<dd>
Indicates if borders must be drawn around the cell block. The value can be either a number:
<ul>
<li><code>0</code>: no border</li>
<li><code>1</code>: frame</li>
</ul>
or a string containing some or all of the following characters (in any order):
<ul>
<li><code>L</code>: left</li>
<li><code>T</code>: top</li>
<li><code>R</code>: right</li>
<li><code>B</code>: bottom</li>
</ul>
Default value: <code>0</code>.
</dd>
<dt><code>align</code></dt>
<dd>
Sets the text alignment. Possible values are:
<ul>
<li><code>L</code>: left alignment</li>
<li><code>C</code>: center</li>
<li><code>R</code>: right alignment</li>
<li><code>J</code>: justification (default value)</li>
</ul>
</dd>
<dt><code>fill</code></dt>
<dd>
Indicates if the cell background must be painted (<code>true</code>) or transparent (<code>false</code>).
Default value: <code>false</code>.
</dd>
</dl>
<h2>See also</h2>
<a href="setfont.htm">SetFont</a>,
<a href="setdrawcolor.htm">SetDrawColor</a>,
<a href="setfillcolor.htm">SetFillColor</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="setlinewidth.htm">SetLineWidth</a>,
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
