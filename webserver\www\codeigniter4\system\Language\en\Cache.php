<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Cache language settings
return [
    'unableToWrite'   => '<PERSON><PERSON> unable to write to "{0}".',
    'invalidHandlers' => 'Cache config must have an array of $validHandlers.',
    'noBackup'        => 'Cache config must have a handler and backupHandler set.',
    'handlerNotFound' => 'Cache config has an invalid handler or backup handler specified.',
];
