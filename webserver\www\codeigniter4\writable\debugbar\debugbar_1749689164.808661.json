{"url": "http://localhost:8050/codeigniter4/public/index.php/notificacoes/contar-nao-lidas", "method": "GET", "isAJAX": false, "startTime": **********.476707, "totalTime": 301.09999999999997, "totalMemory": "4.894", "segmentDuration": 45, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.498868, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.61422, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.63825, "duration": 0.006051778793334961}, {"name": "Before Filters", "component": "Timer", "start": **********.64678, "duration": 1.9073486328125e-05}, {"name": "Controller", "component": "Timer", "start": **********.646801, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.646801, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.770915, "duration": 5.0067901611328125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.770938, "duration": 0.006904125213623047}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(0 total Query, 0  unique across 1 Connection)", "display": {"queries": []}, "badgeValue": 0, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": 0, "duration": "0.000000"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 146 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/Format/Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH/Format/FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH/Format/JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/security_helper.php", "name": "security_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Format.php", "name": "Format.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/NotificacoesController.php", "name": "NotificacoesController.php"}, {"path": "APPPATH/Helpers/functions_helper.php", "name": "functions_helper.php"}, {"path": "APPPATH/Helpers/security_helper.php", "name": "security_helper.php"}, {"path": "APPPATH/Models/Notificacoes.php", "name": "Notificacoes.php"}, {"path": "FCPATH/index.php", "name": "index.php"}]}, "badgeValue": 146, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\NotificacoesController", "method": "contarNaoLidas", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "home", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "cidades", "handler": "\\App\\Controllers\\Cidades::index"}, {"method": "GET", "route": "cidades/index", "handler": "\\App\\Controllers\\Cidades::index"}, {"method": "GET", "route": "cidades/new", "handler": "\\App\\Controllers\\Cidades::new"}, {"method": "GET", "route": "cidades/edit/([0-9]+)", "handler": "\\App\\Controllers\\Cidades::edit/$1"}, {"method": "GET", "route": "cidades/delete/([0-9]+)", "handler": "\\App\\Controllers\\Cidades::delete/$1"}, {"method": "GET", "route": "categorias", "handler": "\\App\\Controllers\\Categorias::index"}, {"method": "GET", "route": "categorias/index", "handler": "\\App\\Controllers\\Categorias::index"}, {"method": "GET", "route": "categorias/new", "handler": "\\App\\Controllers\\Categorias::new"}, {"method": "GET", "route": "categorias/edit/(.*)", "handler": "\\App\\Controllers\\Categorias::edit/$1"}, {"method": "GET", "route": "categorias/delete/(.*)", "handler": "\\App\\Controllers\\Categorias::delete/$1"}, {"method": "GET", "route": "produtos", "handler": "\\App\\Controllers\\Produtos::index"}, {"method": "GET", "route": "produtos/index", "handler": "\\App\\Controllers\\Produtos::index"}, {"method": "GET", "route": "produtos/new", "handler": "\\App\\Controllers\\Produtos::new"}, {"method": "GET", "route": "produtos/edit/(.*)", "handler": "\\App\\Controllers\\Produtos::edit/$1"}, {"method": "GET", "route": "produtos/delete/(.*)", "handler": "\\App\\Controllers\\Produtos::delete/$1"}, {"method": "GET", "route": "usuarios", "handler": "\\App\\Controllers\\Usuarios::index"}, {"method": "GET", "route": "usuarios/index", "handler": "\\App\\Controllers\\Usuarios::index"}, {"method": "GET", "route": "usuarios/new", "handler": "\\App\\Controllers\\Usuarios::new"}, {"method": "GET", "route": "usuarios/edit/(.*)", "handler": "\\App\\Controllers\\Usuarios::edit/$1"}, {"method": "GET", "route": "usuarios/delete/(.*)", "handler": "\\App\\Controllers\\Usuarios::delete/$1"}, {"method": "GET", "route": "usuarios/perfil/(.*)", "handler": "\\App\\Controllers\\Usuarios::perfil/$1"}, {"method": "GET", "route": "usuarios/edit_senha/(.*)", "handler": "\\App\\Controllers\\Usuarios::edit_senha/$1"}, {"method": "GET", "route": "usuarios/acess", "handler": "\\App\\Controllers\\Usuarios::acess"}, {"method": "GET", "route": "usuarios/edit_nivel", "handler": "\\App\\Controllers\\Usuarios::edit_nivel"}, {"method": "GET", "route": "clientes", "handler": "\\App\\Controllers\\Clientes::index"}, {"method": "GET", "route": "clientes/index", "handler": "\\App\\Controllers\\Clientes::index"}, {"method": "GET", "route": "clientes/new", "handler": "\\App\\Controllers\\Clientes::new"}, {"method": "GET", "route": "clientes/edit/([0-9]+)", "handler": "\\App\\Controllers\\Clientes::edit/$1"}, {"method": "GET", "route": "clientes/delete/([0-9]+)", "handler": "\\App\\Controllers\\Clientes::delete/$1"}, {"method": "GET", "route": "funcionarios", "handler": "\\App\\Controllers\\Funcionarios::index"}, {"method": "GET", "route": "funcionarios/index", "handler": "\\App\\Controllers\\Funcionarios::index"}, {"method": "GET", "route": "funcionarios/new", "handler": "\\App\\Controllers\\Funcionarios::new"}, {"method": "GET", "route": "funcionarios/edit/([0-9]+)", "handler": "\\App\\Controllers\\Funcionarios::edit/$1"}, {"method": "GET", "route": "funcionarios/delete/([0-9]+)", "handler": "\\App\\Controllers\\Funcionarios::delete/$1"}, {"method": "GET", "route": "pedidos", "handler": "\\App\\Controllers\\Pedidos::index"}, {"method": "GET", "route": "pedidos/index", "handler": "\\App\\Controllers\\Pedidos::index"}, {"method": "GET", "route": "pedidos/new", "handler": "\\App\\Controllers\\Pedidos::new"}, {"method": "GET", "route": "pedidos/show/([0-9]+)", "handler": "\\App\\Controllers\\Pedidos::show/$1"}, {"method": "GET", "route": "pedidos/edit/([0-9]+)", "handler": "\\App\\Controllers\\Pedidos::edit/$1"}, {"method": "GET", "route": "pedidos/delete/([0-9]+)", "handler": "\\App\\Controllers\\Pedidos::delete/$1"}, {"method": "GET", "route": "pedidos/produto/([0-9]+)", "handler": "\\App\\Controllers\\Pedidos::selectProduto/$1"}, {"method": "GET", "route": "vendas", "handler": "\\App\\Controllers\\Vendas::index"}, {"method": "GET", "route": "vendas/index", "handler": "\\App\\Controllers\\Vendas::index"}, {"method": "GET", "route": "vendas/new", "handler": "\\App\\Controllers\\Vendas::new"}, {"method": "GET", "route": "vendas/edit/([0-9]+)", "handler": "\\App\\Controllers\\Vendas::edit/$1"}, {"method": "GET", "route": "vendas/delete/([0-9]+)", "handler": "\\App\\Controllers\\Vendas::delete/$1"}, {"method": "GET", "route": "vendas/getTotalPedido/([0-9]+)", "handler": "\\App\\Controllers\\Vendas::getTotalPedido/$1"}, {"method": "GET", "route": "enderecos", "handler": "\\App\\Controllers\\Enderecos::index"}, {"method": "GET", "route": "enderecos/index", "handler": "\\App\\Controllers\\Enderecos::index"}, {"method": "GET", "route": "enderecos/new", "handler": "\\App\\Controllers\\Enderecos::new"}, {"method": "GET", "route": "enderecos/edit/(.*)", "handler": "\\App\\Controllers\\Enderecos::edit/$1"}, {"method": "GET", "route": "enderecos/delete/(.*)", "handler": "\\App\\Controllers\\Enderecos::delete/$1"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Login::index"}, {"method": "GET", "route": "login/index", "handler": "\\App\\Controllers\\Login::index"}, {"method": "GET", "route": "login/logout", "handler": "\\App\\Controllers\\Login::logout"}, {"method": "GET", "route": "cadastro", "handler": "\\App\\Controllers\\Cadastro::index"}, {"method": "GET", "route": "cadastro/index", "handler": "\\App\\Controllers\\Cadastro::index"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "admin/index", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "funcionario", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "funcionario/index", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/index", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "imgprodutos", "handler": "\\App\\Controllers\\Imgprodutos::index"}, {"method": "GET", "route": "imgprodutos/index", "handler": "\\App\\Controllers\\Imgprodutos::index"}, {"method": "GET", "route": "imgprodutos/new", "handler": "\\App\\Controllers\\Imgprodutos::new"}, {"method": "GET", "route": "imgprodutos/edit/(.*)", "handler": "\\App\\Controllers\\Imgprodutos::edit/$1"}, {"method": "GET", "route": "imgprodutos/delete/(.*)", "handler": "\\App\\Controllers\\Imgprodutos::delete/$1"}, {"method": "GET", "route": "relatorios/([0-9]+)", "handler": "\\App\\Controllers\\Relatorios::index/$1"}, {"method": "GET", "route": "relatorios/index", "handler": "\\App\\Controllers\\Relatorios::index"}, {"method": "GET", "route": "entregas", "handler": "\\App\\Controllers\\Entregas::index"}, {"method": "GET", "route": "entregas/index", "handler": "\\App\\Controllers\\Entregas::index"}, {"method": "GET", "route": "entregas/new", "handler": "\\App\\Controllers\\Entregas::new"}, {"method": "GET", "route": "entregas/edit/(.*)", "handler": "\\App\\Controllers\\Entregas::edit/$1"}, {"method": "GET", "route": "entregas/delete/([0-9]+)", "handler": "\\App\\Controllers\\Entregas::delete/$1"}, {"method": "GET", "route": "entregas/getEnderecoPorPedido/([0-9]+)", "handler": "\\App\\Controllers\\Entregas::getEnderecoPorPedido/$1"}, {"method": "GET", "route": "estoques", "handler": "\\App\\Controllers\\Estoques::index"}, {"method": "GET", "route": "estoques/index", "handler": "\\App\\Controllers\\Estoques::index"}, {"method": "GET", "route": "estoques/new", "handler": "\\App\\Controllers\\Estoques::new"}, {"method": "GET", "route": "estoques/edit/(.*)", "handler": "\\App\\Controllers\\Estoques::edit/$1"}, {"method": "GET", "route": "estoques/delete/(.*)", "handler": "\\App\\Controllers\\Estoques::delete/$1"}, {"method": "GET", "route": "itens_pedido", "handler": "\\App\\Controllers\\ItensPedido::index"}, {"method": "GET", "route": "itens_pedido/index", "handler": "\\App\\Controllers\\ItensPedido::index"}, {"method": "GET", "route": "itens_pedido/new", "handler": "\\App\\Controllers\\ItensPedido::new"}, {"method": "GET", "route": "itens_pedido/edit/(.*)", "handler": "\\App\\Controllers\\ItensPedido::edit/$1"}, {"method": "GET", "route": "itens_pedido/delete/(.*)", "handler": "\\App\\Controllers\\ItensPedido::delete/$1"}, {"method": "GET", "route": "itens_pedido/finalizar/([0-9]+)", "handler": "\\App\\Controllers\\ItensPedido::finalizar_pedido/$1"}, {"method": "GET", "route": "<PERSON><PERSON><PERSON>", "handler": "\\App\\Controllers\\CarrinhoController::index"}, {"method": "GET", "route": "carrinho/limpar", "handler": "\\App\\Controllers\\CarrinhoController::limpar"}, {"method": "GET", "route": "carrinho/contar-itens", "handler": "\\App\\Controllers\\CarrinhoController::contarItens"}, {"method": "GET", "route": "avaliacoes/produto/([0-9]+)", "handler": "\\App\\Controllers\\AvaliacoesController::produto/$1"}, {"method": "GET", "route": "avaliacoes/adicionar/([0-9]+)", "handler": "\\App\\Controllers\\AvaliacoesController::adicionar/$1"}, {"method": "GET", "route": "avaliacoes/editar/([0-9]+)", "handler": "\\App\\Controllers\\AvaliacoesController::editar/$1"}, {"method": "GET", "route": "avaliacoes/remover/([0-9]+)", "handler": "\\App\\Controllers\\AvaliacoesController::remover/$1"}, {"method": "GET", "route": "avaliacoes/recentes", "handler": "\\App\\Controllers\\AvaliacoesController::recentes"}, {"method": "GET", "route": "cupons", "handler": "\\App\\Controllers\\CuponsController::index"}, {"method": "GET", "route": "cupons/novo", "handler": "\\App\\Controllers\\CuponsController::novo"}, {"method": "GET", "route": "cupons/editar/([0-9]+)", "handler": "\\App\\Controllers\\CuponsController::editar/$1"}, {"method": "GET", "route": "cupons/remover/([0-9]+)", "handler": "\\App\\Controllers\\CuponsController::remover/$1"}, {"method": "GET", "route": "cupons/disponiveis", "handler": "\\App\\Controllers\\CuponsController::disponiveis"}, {"method": "GET", "route": "notificacoes", "handler": "\\App\\Controllers\\NotificacoesController::index"}, {"method": "GET", "route": "notificacoes/limpar-lidas", "handler": "\\App\\Controllers\\NotificacoesController::limparLidas"}, {"method": "GET", "route": "notificacoes/contar-nao-lidas", "handler": "\\App\\Controllers\\NotificacoesController::contarNaoLidas"}, {"method": "GET", "route": "notificacoes/nao-lidas", "handler": "\\App\\Controllers\\NotificacoesController::naoLidas"}, {"method": "GET", "route": "notificacoes/form-geral", "handler": "\\App\\Controllers\\NotificacoesController::formEnviarGeral"}, {"method": "GET", "route": "notificacoes/estatisticas", "handler": "\\App\\Controllers\\NotificacoesController::estatisticas"}, {"method": "GET", "route": "notificacoes/limpar-antigas", "handler": "\\App\\Controllers\\NotificacoesController::limparAntigas"}, {"method": "GET", "route": "api/produtos", "handler": "\\App\\Controllers\\Api\\ProdutosApi::index"}, {"method": "GET", "route": "api/produtos/new", "handler": "\\App\\Controllers\\Api\\ProdutosApi::new"}, {"method": "GET", "route": "api/produtos/(.*)/edit", "handler": "\\App\\Controllers\\Api\\ProdutosApi::edit/$1"}, {"method": "GET", "route": "api/produtos/(.*)", "handler": "\\App\\Controllers\\Api\\ProdutosApi::show/$1"}, {"method": "GET", "route": "api/produtos/categoria/([0-9]+)", "handler": "\\App\\Controllers\\Api\\ProdutosApi::porCategoria/$1"}, {"method": "GET", "route": "api/carrinho", "handler": "\\App\\Controllers\\Api\\CarrinhoApi::index"}, {"method": "GET", "route": "api/avaliacoes/produto/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AvaliacoesApi::produto/$1"}, {"method": "GET", "route": "api/cupons/disponiveis", "handler": "\\App\\Controllers\\Api\\CuponsApi::disponiveis"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\DashboardController::index"}, {"method": "GET", "route": "dashboard/funcionario", "handler": "\\App\\Controllers\\DashboardController::funcionario"}, {"method": "GET", "route": "dashboard/cliente", "handler": "\\App\\Controllers\\DashboardController::cliente"}, {"method": "GET", "route": "dashboard/api/([^/]+)", "handler": "\\App\\Controllers\\DashboardController::apiDados/$1"}, {"method": "GET", "route": "rastrear/([0-9]+)", "handler": "\\App\\Controllers\\RastreamentoController::rastrear/$1"}, {"method": "GET", "route": "rastreamento/status/([0-9]+)", "handler": "\\App\\Controllers\\RastreamentoController::statusAtual/$1"}, {"method": "GET", "route": "rastreamento/gerenciar", "handler": "\\App\\Controllers\\RastreamentoController::gerenciar"}, {"method": "POST", "route": "cidades/create", "handler": "\\App\\Controllers\\Cidades::create"}, {"method": "POST", "route": "cidades/update", "handler": "\\App\\Controllers\\Cidades::update"}, {"method": "POST", "route": "cidades/search", "handler": "\\App\\Controllers\\Cidades::search"}, {"method": "POST", "route": "categorias/create", "handler": "\\App\\Controllers\\Categorias::create"}, {"method": "POST", "route": "categorias/update", "handler": "\\App\\Controllers\\Categorias::update"}, {"method": "POST", "route": "categorias/search", "handler": "\\App\\Controllers\\Categorias::search"}, {"method": "POST", "route": "produtos/create", "handler": "\\App\\Controllers\\Produtos::create"}, {"method": "POST", "route": "produtos/update", "handler": "\\App\\Controllers\\Produtos::update"}, {"method": "POST", "route": "produtos/search", "handler": "\\App\\Controllers\\Produtos::search"}, {"method": "POST", "route": "usuarios/create", "handler": "\\App\\Controllers\\Usuarios::create"}, {"method": "POST", "route": "usuarios/update", "handler": "\\App\\Controllers\\Usuarios::update"}, {"method": "POST", "route": "usuarios/search", "handler": "\\App\\Controllers\\Usuarios::search"}, {"method": "POST", "route": "usuarios/salvar_senha", "handler": "\\App\\Controllers\\Usuarios::salvar_senha"}, {"method": "POST", "route": "usuarios/salvar_nivel", "handler": "\\App\\Controllers\\Usuarios::salvar_nivel"}, {"method": "POST", "route": "clientes/create", "handler": "\\App\\Controllers\\Clientes::create"}, {"method": "POST", "route": "clientes/update", "handler": "\\App\\Controllers\\Clientes::update"}, {"method": "POST", "route": "clientes/search", "handler": "\\App\\Controllers\\Clientes::search"}, {"method": "POST", "route": "funcionarios/create", "handler": "\\App\\Controllers\\Funcionarios::create"}, {"method": "POST", "route": "funcionarios/update", "handler": "\\App\\Controllers\\Funcionarios::update"}, {"method": "POST", "route": "funcionarios/search", "handler": "\\App\\Controllers\\Funcionarios::search"}, {"method": "POST", "route": "pedidos/create", "handler": "\\App\\Controllers\\Pedidos::create"}, {"method": "POST", "route": "pedidos/createPedido", "handler": "\\App\\Controllers\\Pedidos::createPedido"}, {"method": "POST", "route": "pedidos/update", "handler": "\\App\\Controllers\\Pedidos::update"}, {"method": "POST", "route": "pedidos/search", "handler": "\\App\\Controllers\\Pedidos::search"}, {"method": "POST", "route": "vendas/create", "handler": "\\App\\Controllers\\Vendas::create"}, {"method": "POST", "route": "vendas/update", "handler": "\\App\\Controllers\\Vendas::update"}, {"method": "POST", "route": "vendas/search", "handler": "\\App\\Controllers\\Vendas::search"}, {"method": "POST", "route": "enderecos/create", "handler": "\\App\\Controllers\\Enderecos::create"}, {"method": "POST", "route": "enderecos/update", "handler": "\\App\\Controllers\\Enderecos::update"}, {"method": "POST", "route": "enderecos/search", "handler": "\\App\\Controllers\\Enderecos::search"}, {"method": "POST", "route": "login/logar", "handler": "\\App\\Controllers\\Login::logar"}, {"method": "POST", "route": "cadastro/salvar", "handler": "\\App\\Controllers\\Cadastro::salvar"}, {"method": "POST", "route": "imgprodutos/create", "handler": "\\App\\Controllers\\Imgprodutos::create"}, {"method": "POST", "route": "imgprodutos/update", "handler": "\\App\\Controllers\\Imgprodutos::update"}, {"method": "POST", "route": "imgprodutos/search", "handler": "\\App\\Controllers\\Imgprodutos::search"}, {"method": "POST", "route": "entregas/create", "handler": "\\App\\Controllers\\Entregas::create"}, {"method": "POST", "route": "entregas/update", "handler": "\\App\\Controllers\\Entregas::update"}, {"method": "POST", "route": "entregas/search", "handler": "\\App\\Controllers\\Entregas::search"}, {"method": "POST", "route": "estoques/create", "handler": "\\App\\Controllers\\Estoques::create"}, {"method": "POST", "route": "estoques/update", "handler": "\\App\\Controllers\\Estoques::update"}, {"method": "POST", "route": "estoques/search", "handler": "\\App\\Controllers\\Estoques::search"}, {"method": "POST", "route": "itens_pedido/create", "handler": "\\App\\Controllers\\ItensPedido::create"}, {"method": "POST", "route": "itens_pedido/update", "handler": "\\App\\Controllers\\ItensPedido::update"}, {"method": "POST", "route": "itens_pedido/search", "handler": "\\App\\Controllers\\ItensPedido::search"}, {"method": "POST", "route": "carrinho/adicionar", "handler": "\\App\\Controllers\\CarrinhoController::adicionar"}, {"method": "POST", "route": "carrinho/atualizar", "handler": "\\App\\Controllers\\CarrinhoController::atualizarQuantidade"}, {"method": "POST", "route": "carrinho/remover", "handler": "\\App\\Controllers\\CarrinhoController::remover"}, {"method": "POST", "route": "carrinho/aplicar-cupom", "handler": "\\App\\Controllers\\CarrinhoController::aplicarCupom"}, {"method": "POST", "route": "carrinho/remover-cupom", "handler": "\\App\\Controllers\\CarrinhoController::removerCupom"}, {"method": "POST", "route": "avaliacoes/salvar", "handler": "\\App\\Controllers\\AvaliacoesController::salvar"}, {"method": "POST", "route": "avaliacoes/atualizar", "handler": "\\App\\Controllers\\AvaliacoesController::atualizar"}, {"method": "POST", "route": "avaliacoes/moderar", "handler": "\\App\\Controllers\\AvaliacoesController::moderar"}, {"method": "POST", "route": "cupons/criar", "handler": "\\App\\Controllers\\CuponsController::criar"}, {"method": "POST", "route": "cupons/atualizar", "handler": "\\App\\Controllers\\CuponsController::atualizar"}, {"method": "POST", "route": "cupons/toggle-status", "handler": "\\App\\Controllers\\CuponsController::toggleStatus"}, {"method": "POST", "route": "cupons/validar", "handler": "\\App\\Controllers\\CuponsController::validar"}, {"method": "POST", "route": "cupons/gerar-codigo", "handler": "\\App\\Controllers\\CuponsController::gerarCodigo"}, {"method": "POST", "route": "notificacoes/marcar-lida", "handler": "\\App\\Controllers\\NotificacoesController::marcarLida"}, {"method": "POST", "route": "notificacoes/marcar-todas-lidas", "handler": "\\App\\Controllers\\NotificacoesController::marcarTodasLidas"}, {"method": "POST", "route": "notificacoes/remover", "handler": "\\App\\Controllers\\NotificacoesController::remover"}, {"method": "POST", "route": "notificacoes/enviar-todos", "handler": "\\App\\Controllers\\NotificacoesController::enviarParaTodos"}, {"method": "POST", "route": "api/produtos", "handler": "\\App\\Controllers\\Api\\ProdutosApi::create"}, {"method": "POST", "route": "api/carrinho/adicionar", "handler": "\\App\\Controllers\\Api\\CarrinhoApi::adicionar"}, {"method": "POST", "route": "api/avaliacoes", "handler": "\\App\\Controllers\\Api\\AvaliacoesApi::criar"}, {"method": "POST", "route": "api/cupons/validar", "handler": "\\App\\Controllers\\Api\\CuponsApi::validar"}, {"method": "POST", "route": "rastreamento/atualizar-status", "handler": "\\App\\Controllers\\RastreamentoController::atualizarStatus"}, {"method": "PATCH", "route": "api/produtos/(.*)", "handler": "\\App\\Controllers\\Api\\ProdutosApi::update/$1"}, {"method": "PUT", "route": "api/produtos/(.*)", "handler": "\\App\\Controllers\\Api\\ProdutosApi::update/$1"}, {"method": "PUT", "route": "api/carrinho/([0-9]+)", "handler": "\\App\\Controllers\\Api\\CarrinhoApi::atualizar/$1"}, {"method": "PUT", "route": "api/avaliacoes/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AvaliacoesApi::atualizar/$1"}, {"method": "DELETE", "route": "api/produtos/(.*)", "handler": "\\App\\Controllers\\Api\\ProdutosApi::delete/$1"}, {"method": "DELETE", "route": "api/carrinho/([0-9]+)", "handler": "\\App\\Controllers\\Api\\CarrinhoApi::remover/$1"}, {"method": "DELETE", "route": "api/avaliacoes/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AvaliacoesApi::remover/$1"}]}, "badgeValue": 125, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "48.49", "count": 1}}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.565721, "duration": 0.0484929084777832}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1749688989</pre>", "_ci_previous_url": "http://localhost:8050/codeigniter4/public/index.php/login"}, "headers": {"Host": "localhost:8050", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Sec-Ch-Ua": "&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:8050/codeigniter4/public/index.php/login", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Cookie": "pma_lang=pt_BR; ci_session=c6241a6b2e623df908bd1a547d2794ef; phpMyAdmin=5ff5135dab83ef3cfef427e95c36f6da; pmaUser-1=shIAfwevDJdkTkcby6pZzYx6c0C0heP3DS3gu12f1EWoeFdSkk9VpvKWI5s%3D; pmaAuth-1=wVFUH%2FSeAcpH8K5G%2FKYgOYfeRJbJMprQVqDC2jcQfVWt%2FJbl3ivB8EKA23w9%2B%2FHNUfUUsdVoZFBSInY%3D"}, "cookies": {"pma_lang": "pt_BR", "ci_session": "c6241a6b2e623df908bd1a547d2794ef", "phpMyAdmin": "5ff5135dab83ef3cfef427e95c36f6da", "pmaUser-1": "shIAfwevDJdkTkcby6pZzYx6c0C0heP3DS3gu12f1EWoeFdSkk9VpvKWI5s=", "pmaAuth-1": "wVFUH/SeAcpH8K5G/KYgOYfeRJbJMprQVqDC2jcQfVWt/Jbl3ivB8EKA23w9+/HNUfUUsdVoZFBSInY="}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Content-Type": "application/json; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.28", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost:8050/codeigniter4/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}